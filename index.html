<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitTrack - Gym Member Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gym-primary': '#33AADA', // Emerald green
                        'gym-secondary': '#F59E0B', // Amber
                        'gym-accent': '#3B82F6', // Blue
                        'gym-blue': '#337ADE', // Custom blue accent
                        'gym-dark': '#111827', // Gray 900
                        'gym-darker': '#030712', // Gray 950
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            /* Dark theme colors (default) */
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --bg-tertiary: #374151;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --border-primary: #374151;
            --border-secondary: #4b5563;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --card-bg: #111827;
            --input-bg: #374151;
            --hover-bg: rgba(55, 65, 81, 0.5);

            /* Dark theme subtle gradient colors */
            --gradient-primary-dark: linear-gradient(135deg, #8554F5 0%, #5D42EE 20%, #4268E4 40%, #337ADE 60%, #33AADA 80%, #3FE0D0 100%);
            --gradient-subtle-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.05) 0%, rgba(93, 66, 238, 0.05) 20%, rgba(66, 104, 228, 0.05) 40%, rgba(51, 122, 222, 0.05) 60%, rgba(51, 170, 218, 0.05) 80%, rgba(63, 224, 208, 0.05) 100%);
            --gradient-border-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.1) 0%, rgba(93, 66, 238, 0.1) 20%, rgba(66, 104, 228, 0.1) 40%, rgba(51, 122, 222, 0.1) 60%, rgba(51, 170, 218, 0.1) 80%, rgba(63, 224, 208, 0.1) 100%);
            --gradient-hover-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.08) 0%, rgba(93, 66, 238, 0.08) 20%, rgba(66, 104, 228, 0.08) 40%, rgba(51, 122, 222, 0.08) 60%, rgba(51, 170, 218, 0.08) 80%, rgba(63, 224, 208, 0.08) 100%);
            --gradient-accent-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.15) 0%, rgba(93, 66, 238, 0.15) 20%, rgba(66, 104, 228, 0.15) 40%, rgba(51, 122, 222, 0.15) 60%, rgba(51, 170, 218, 0.15) 80%, rgba(63, 224, 208, 0.15) 100%);
        }

        [data-theme="light"] {
            /* Light theme colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-primary: #e2e8f0;
            --border-secondary: #cbd5e1;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --card-bg: #ffffff;
            --input-bg: #f1f5f9;
            --hover-bg: rgba(241, 245, 249, 0.8);

            /* Vibrant gradient colors */
            --gradient-primary: linear-gradient(135deg, #8554F5 0%, #5D42EE 20%, #4268E4 40%, #337ADE 60%, #33AADA 80%, #3FE0D0 100%);
            --gradient-subtle: linear-gradient(135deg, rgba(133, 84, 245, 0.1) 0%, rgba(93, 66, 238, 0.1) 20%, rgba(66, 104, 228, 0.1) 40%, rgba(51, 122, 222, 0.1) 60%, rgba(51, 170, 218, 0.1) 80%, rgba(63, 224, 208, 0.1) 100%);
            --gradient-border: linear-gradient(135deg, rgba(133, 84, 245, 0.3) 0%, rgba(93, 66, 238, 0.3) 20%, rgba(66, 104, 228, 0.3) 40%, rgba(51, 122, 222, 0.3) 60%, rgba(51, 170, 218, 0.3) 80%, rgba(63, 224, 208, 0.3) 100%);
            --gradient-hover: linear-gradient(135deg, rgba(133, 84, 245, 0.15) 0%, rgba(93, 66, 238, 0.15) 20%, rgba(66, 104, 228, 0.15) 40%, rgba(51, 122, 222, 0.15) 60%, rgba(51, 170, 218, 0.15) 80%, rgba(63, 224, 208, 0.15) 100%);
        }

        /* Theme transition */
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Theme toggle button styles */
        .theme-toggle {
            position: relative;
            width: 60px;
            height: 30px;
            background: var(--border-secondary);
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: var(--text-primary);
            border-radius: 50%;
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        [data-theme="light"] .theme-toggle::before {
            transform: translateX(30px);
        }

        .theme-toggle-icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            transition: opacity 0.3s ease;
        }

        .theme-toggle .sun-icon {
            right: 8px;
            color: #f59e0b;
            opacity: 0;
        }

        .theme-toggle .moon-icon {
            left: 8px;
            color: #6366f1;
            opacity: 1;
        }

        [data-theme="light"] .theme-toggle .sun-icon {
            opacity: 1;
        }

        [data-theme="light"] .theme-toggle .moon-icon {
            opacity: 0;
        }

        /* Navigation link styles */
        .nav-link {
            color: var(--text-secondary);
        }

        .nav-link:hover {
            color: var(--text-primary);
            background-color: var(--hover-bg);
        }

        .nav-link.active {
            color: var(--text-primary);
            background-color: var(--hover-bg);
        }

        /* Input placeholder styles */
        input::placeholder {
            color: var(--text-muted);
        }

        /* Custom scrollbar for light theme */
        [data-theme="light"] .sidebar-scroll::-webkit-scrollbar-track {
            background: transparent;
        }

        /* Hidden scrollbar by default - Light theme */
        [data-theme="light"] .sidebar-scroll::-webkit-scrollbar-thumb {
            background: transparent;
            transition: background-color 0.3s ease, opacity 0.3s ease;
        }

        [data-theme="light"] .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(100, 116, 139, 0.8);
        }

        /* Show scrollbar when actively scrolling - Light theme */
        [data-theme="light"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
            background: rgba(203, 213, 225, 0.8);
        }

        [data-theme="light"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb:hover {
            background: rgba(100, 116, 139, 1);
        }

        /* Update fade effects for light theme */
        [data-theme="light"] .sidebar-scroll::before {
            background: linear-gradient(to bottom, var(--card-bg), transparent);
        }

        [data-theme="light"] .sidebar-scroll::after {
            background: linear-gradient(to top, var(--card-bg), transparent);
        }

        /* Light theme gradient enhancements */

        /* Header gradient background */
        [data-theme="light"] header {
            background: var(--gradient-subtle) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(133, 84, 245, 0.2) !important;
        }

        /* Stats cards with gradient borders and subtle backgrounds */
        [data-theme="light"] .stats-card {
            background: var(--gradient-subtle);
            border: 1px solid transparent;
            background-clip: padding-box;
            position: relative;
        }

        [data-theme="light"] .stats-card::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            background: var(--gradient-border);
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            pointer-events: none;
        }

        /* Quick action buttons with gradient hover effects */
        [data-theme="light"] .quick-action-btn:hover {
            background: var(--gradient-hover) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 20px rgba(133, 84, 245, 0.2);
        }

        /* Active navigation item gradient accent */
        [data-theme="light"] .nav-link.active {
            background: var(--gradient-subtle) !important;
            border-left: 3px solid #8554F5;
            position: relative;
        }

        [data-theme="light"] .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--gradient-primary);
        }

        /* Theme toggle gradient background in light mode */
        [data-theme="light"] .theme-toggle {
            background: var(--gradient-primary) !important;
            box-shadow: 0 2px 10px rgba(133, 84, 245, 0.3);
        }

        /* Recent activity cards with subtle gradient */
        [data-theme="light"] .activity-card {
            background: var(--gradient-subtle);
            border: 1px solid rgba(133, 84, 245, 0.1);
        }

        /* Profile avatar gradient border */
        [data-theme="light"] .profile-avatar {
            background: var(--gradient-primary);
            padding: 2px;
        }

        [data-theme="light"] .profile-avatar > div {
            background: white;
            border-radius: inherit;
        }

        /* Notification dropdown gradient accent */
        [data-theme="light"] #notification-dropdown {
            background: var(--gradient-subtle) !important;
            border: 1px solid rgba(133, 84, 245, 0.2) !important;
            backdrop-filter: blur(10px);
        }

        /* Search input gradient focus */
        [data-theme="light"] input:focus {
            box-shadow: 0 0 0 3px rgba(133, 84, 245, 0.1) !important;
            border-color: #8554F5 !important;
        }

        /* Gradient text for special elements */
        [data-theme="light"] .gradient-text {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        /* Dark theme gradient enhancements - Subtle and Professional */

        /* Header subtle gradient accent */
        [data-theme="dark"] header {
            background: linear-gradient(135deg, var(--card-bg) 0%, var(--card-bg) 70%, rgba(133, 84, 245, 0.03) 100%) !important;
            border-bottom: 1px solid var(--border-primary) !important;
            position: relative;
        }

        [data-theme="dark"] header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-border-dark);
            opacity: 0.5;
        }

        /* Stats cards with subtle gradient borders */
        [data-theme="dark"] .stats-card {
            background: var(--card-bg);
            border: 1px solid var(--border-primary);
            position: relative;
            overflow: hidden;
        }

        [data-theme="dark"] .stats-card::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            background: var(--gradient-border-dark);
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            pointer-events: none;
            opacity: 0.6;
        }

        [data-theme="dark"] .stats-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: var(--gradient-subtle-dark);
            pointer-events: none;
            opacity: 0.3;
        }

        /* Quick action buttons with subtle gradient hover */
        [data-theme="dark"] .quick-action-btn:hover {
            background: var(--gradient-hover-dark) !important;
            border: 1px solid rgba(133, 84, 245, 0.1);
            box-shadow: 0 2px 8px rgba(133, 84, 245, 0.05);
        }

        /* Active navigation item subtle gradient accent */
        [data-theme="dark"] .nav-link.active {
            background: var(--gradient-subtle-dark) !important;
            border-left: 2px solid rgba(133, 84, 245, 0.4);
            position: relative;
        }

        [data-theme="dark"] .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--gradient-primary-dark);
            opacity: 0.6;
        }

        /* Theme toggle subtle gradient in dark mode */
        [data-theme="dark"] .theme-toggle {
            background: linear-gradient(135deg, var(--border-secondary) 0%, rgba(133, 84, 245, 0.1) 100%) !important;
            border: 1px solid rgba(133, 84, 245, 0.1);
        }

        /* Recent activity cards with minimal gradient accent */
        [data-theme="dark"] .activity-card {
            background: var(--hover-bg);
            border: 1px solid rgba(133, 84, 245, 0.05);
            position: relative;
        }

        [data-theme="dark"] .activity-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-border-dark);
            opacity: 0.3;
        }

        /* Profile avatar subtle gradient border */
        [data-theme="dark"] .profile-avatar {
            background: var(--gradient-border-dark);
            padding: 1px;
        }

        /* Notification dropdown subtle gradient accent */
        [data-theme="dark"] #notification-dropdown {
            background: var(--card-bg) !important;
            border: 1px solid var(--border-primary) !important;
            position: relative;
        }

        [data-theme="dark"] #notification-dropdown::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: var(--gradient-subtle-dark);
            pointer-events: none;
            opacity: 0.2;
            border-radius: inherit;
        }

        /* Search input subtle gradient focus */
        [data-theme="dark"] input:focus {
            box-shadow: 0 0 0 2px rgba(133, 84, 245, 0.1) !important;
            border-color: rgba(133, 84, 245, 0.3) !important;
        }

        /* Gradient text for dark theme - more subtle */
        [data-theme="dark"] .gradient-text {
            background: var(--gradient-primary-dark);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            opacity: 0.9;
        }

        /* Sidebar subtle gradient enhancement */
        [data-theme="dark"] #sidebar {
            background: linear-gradient(180deg, var(--card-bg) 0%, var(--card-bg) 90%, rgba(133, 84, 245, 0.02) 100%) !important;
            border-right: 1px solid var(--border-primary) !important;
            position: relative;
        }

        [data-theme="dark"] #sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 1px;
            background: var(--gradient-border-dark);
            opacity: 0.3;
        }

        /* Main content area subtle gradient */
        [data-theme="dark"] main {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-primary) 95%, rgba(133, 84, 245, 0.01) 100%);
        }

        /* Card containers subtle enhancement */
        [data-theme="dark"] .rounded-xl {
            position: relative;
        }

        [data-theme="dark"] .rounded-xl:not(.stats-card):not(.activity-card):not(.quick-action-btn) {
            background: var(--card-bg);
            border: 1px solid var(--border-primary);
        }

        [data-theme="dark"] .rounded-xl:not(.stats-card):not(.activity-card):not(.quick-action-btn)::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-border-dark);
            opacity: 0.2;
            border-radius: inherit;
        }

        /* Navigation hover effects with subtle gradient */
        [data-theme="dark"] .nav-link:hover:not(.active) {
            background: var(--gradient-subtle-dark) !important;
            border-left: 1px solid rgba(133, 84, 245, 0.2);
        }

        /* Button and interactive element enhancements */
        [data-theme="dark"] button:hover {
            box-shadow: 0 1px 4px rgba(133, 84, 245, 0.05);
        }

        /* Scrollbar gradient enhancement for dark theme */
        [data-theme="dark"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, rgba(75, 85, 99, 0.8) 0%, rgba(133, 84, 245, 0.3) 100%) !important;
        }

        /* Mobile menu overlay gradient */
        [data-theme="dark"] #mobile-menu-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.5) 0%, rgba(133, 84, 245, 0.1) 100%);
        }

        /* Notification badge subtle gradient */
        [data-theme="dark"] #notification-badge {
            background: var(--gradient-accent-dark);
            border: 1px solid rgba(133, 84, 245, 0.2);
        }

        /* Form elements subtle gradient focus */
        [data-theme="dark"] select:focus,
        [data-theme="dark"] textarea:focus {
            box-shadow: 0 0 0 2px rgba(133, 84, 245, 0.1) !important;
            border-color: rgba(133, 84, 245, 0.3) !important;
        }

        /* Subtle animation for gradient elements */
        [data-theme="dark"] .stats-card,
        [data-theme="dark"] .activity-card,
        [data-theme="dark"] .quick-action-btn {
            transition: all 0.3s ease, box-shadow 0.3s ease;
        }

        /* Hover enhancement for cards */
        [data-theme="dark"] .stats-card:hover::before {
            opacity: 0.8;
        }

        [data-theme="dark"] .stats-card:hover::after {
            opacity: 0.4;
        }

        /* Focus states for accessibility */
        [data-theme="dark"] *:focus-visible {
            outline: 2px solid rgba(133, 84, 245, 0.4);
            outline-offset: 2px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans" style="background-color: var(--bg-primary); color: var(--text-primary);" data-theme="dark">
    <!-- Mobile menu overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

    <!-- Main Layout Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col" style="background-color: var(--card-bg); border-right: 1px solid var(--border-primary);">
            <!-- Fixed Sidebar Header -->
            <div class="flex items-center justify-between h-16 px-6 flex-shrink-0" style="border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gym-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white text-sm"></i>
                    </div>
                    <span class="gradient-text text-xl font-bold text-white">FitTrack</span>
                </div>
                <button id="close-sidebar" class="lg:hidden text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Scrollable Navigation Container -->
            <div class="flex-1 overflow-y-auto sidebar-scroll">
                <nav class="mt-6 px-3 pb-6">
            <div class="space-y-1">
                <a href="#dashboard" class="nav-link active flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors" style="color: var(--text-primary); background-color: var(--hover-bg);">
                    <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                    Dashboard
                </a>
                <a href="#members" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-users w-5 h-5 mr-3"></i>
                    Members
                </a>
                <a href="#registration" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-user-plus w-5 h-5 mr-3"></i>
                    Registration
                </a>
                <a href="#checkin" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-sign-in-alt w-5 h-5 mr-3"></i>
                    Check-in/Out
                </a>
                <a href="#memberships" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-credit-card w-5 h-5 mr-3"></i>
                    Memberships
                </a>
                <a href="#billing" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-receipt w-5 h-5 mr-3"></i>
                    Billing
                </a>
                <a href="#schedule" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-calendar-alt w-5 h-5 mr-3"></i>
                    Class Schedule
                </a>
                <a href="#statistics" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-chart-bar w-5 h-5 mr-3"></i>
                    Statistics
                </a>
            </div>

            <!-- Additional Menu Items for Testing Scroll -->
            <div class="mt-8 pt-6 border-t border-gray-800">
                <div class="space-y-1">
                    <a href="#trainers" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-user-tie w-5 h-5 mr-3"></i>
                        Trainers
                    </a>
                    <a href="#equipment" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-dumbbell w-5 h-5 mr-3"></i>
                        Equipment
                    </a>
                    <a href="#inventory" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-boxes w-5 h-5 mr-3"></i>
                        Inventory
                    </a>
                    <a href="#maintenance" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-wrench w-5 h-5 mr-3"></i>
                        Maintenance
                    </a>
                    <a href="#reports" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-file-alt w-5 h-5 mr-3"></i>
                        Reports
                    </a>
                    <a href="#analytics" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-chart-line w-5 h-5 mr-3"></i>
                        Analytics
                    </a>
                    <a href="#notifications-page" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-bell w-5 h-5 mr-3"></i>
                        All Notifications
                    </a>
                    <a href="#help" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-question-circle w-5 h-5 mr-3"></i>
                        Help & Support
                    </a>
                </div>
            </div>

            <div class="mt-8 pt-6 border-t border-gray-800">
                <div class="space-y-1">
                    <a href="#settings" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-cog w-5 h-5 mr-3"></i>
                        Settings
                    </a>
                    <a href="#profile" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-user-circle w-5 h-5 mr-3"></i>
                        Profile
                    </a>
                    <a href="#logout" class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors text-red-400 hover:text-red-300 hover:bg-red-900/20">
                        <i class="fas fa-sign-out-alt w-5 h-5 mr-3"></i>
                        Logout
                    </a>
                </div>
                </div>
                </nav>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top header -->
            <header class="px-4 py-4 sm:px-6 lg:px-8 flex-shrink-0" style="background-color: var(--card-bg); border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button id="open-sidebar" class="lg:hidden text-gray-400 hover:text-white">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <div>
                            <h1 class="gradient-text text-2xl font-bold text-white">Dashboard</h1>
                            <p class="text-gray-400 text-sm">Welcome back, Admin</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Theme Toggle -->
                        <div class="flex items-center space-x-3">
                            <span class="text-sm font-medium" style="color: var(--text-secondary);">Theme</span>
                            <div id="theme-toggle" class="theme-toggle">
                                <i class="fas fa-moon theme-toggle-icon moon-icon"></i>
                                <i class="fas fa-sun theme-toggle-icon sun-icon"></i>
                            </div>
                        </div>

                        <!-- Search -->
                        <div class="hidden sm:block relative">
                            <input type="text" placeholder="Search members..."
                                   class="rounded-lg px-4 py-2 pl-10 text-sm focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                   style="background-color: var(--input-bg); border: 1px solid var(--border-secondary); color: var(--text-primary);"
                                   onchange="this.style.color = 'var(--text-primary)'"
                                   onfocus="this.style.color = 'var(--text-primary)'">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2" style="color: var(--text-muted);"></i>
                        </div>

                        <!-- Notifications -->
                        <div class="relative">
                            <button id="notification-btn" class="relative p-2 text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-bell text-lg"></i>
                                <span id="notification-badge" class="absolute -top-1 -right-1 bg-gym-secondary text-xs text-white rounded-full w-5 h-5 flex items-center justify-center">3</span>
                            </button>

                            <!-- Notification Dropdown -->
                            <div id="notification-dropdown" class="w-80 rounded-xl shadow-2xl hidden" style="background-color: var(--card-bg); border: 1px solid var(--border-primary); position: fixed; z-index: 9999;">
                                <!-- Dropdown Header -->
                                <div class="p-4" style="border-bottom: 1px solid var(--border-primary);">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Notifications</h3>
                                        <button id="mark-all-read" class="text-gym-primary hover:text-gym-primary/80 text-sm font-medium transition-colors">
                                            Mark all read
                                        </button>
                                    </div>
                                </div>

                                <!-- Notifications List -->
                                <div id="notifications-list" class="max-h-96 overflow-y-auto">
                                    <!-- Notification items will be dynamically inserted here -->
                                </div>

                                <!-- Dropdown Footer -->
                                <div class="p-4" style="border-top: 1px solid var(--border-primary);">
                                    <button id="view-all-notifications" class="w-full text-center text-gym-primary hover:text-gym-primary/80 text-sm font-medium transition-colors">
                                        View all notifications
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Profile -->
                        <div class="flex items-center space-x-3">
                            <div class="profile-avatar w-8 h-8 rounded-full flex items-center justify-center" style="background: #10B981;">
                                <div class="w-full h-full bg-gym-primary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                            </div>
                            <span class="hidden sm:block text-sm font-medium text-white">Admin User</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main content area -->
            <main class="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8">
            <!-- Dashboard Section -->
            <div id="dashboard-section" class="section active">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stats-card rounded-xl p-6" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium" style="color: var(--text-muted);">Total Members</p>
                                <p class="text-3xl font-bold mt-1" style="color: var(--text-primary);">1,247</p>
                                <p class="text-gym-primary text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+12% from last month
                                </p>
                            </div>
                            <div class="w-12 h-12 bg-gym-primary/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-gym-primary text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card rounded-xl p-6" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium" style="color: var(--text-muted);">Active Today</p>
                                <p class="text-3xl font-bold mt-1" style="color: var(--text-primary);">89</p>
                                <p class="text-gym-secondary text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+5% from yesterday
                                </p>
                            </div>
                            <div class="w-12 h-12 bg-gym-secondary/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-sign-in-alt text-gym-secondary text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card rounded-xl p-6" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium" style="color: var(--text-muted);">Revenue</p>
                                <p class="text-3xl font-bold mt-1" style="color: var(--text-primary);">$24,580</p>
                                <p class="text-gym-accent text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+8% from last month
                                </p>
                            </div>
                            <div class="w-12 h-12 bg-gym-accent/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-gym-accent text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card rounded-xl p-6" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium" style="color: var(--text-muted);">Classes Today</p>
                                <p class="text-3xl font-bold mt-1" style="color: var(--text-primary);">12</p>
                                <p class="text-gym-blue text-sm mt-1">
                                    <i class="fas fa-check mr-1"></i>All scheduled
                                </p>
                            </div>
                            <div class="w-12 h-12 bg-gym-blue/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-calendar-check text-gym-blue text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions & Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                    <!-- Quick Actions -->
                    <div class="lg:col-span-1">
                        <div class="rounded-xl p-6" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                            <h3 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Quick Actions</h3>
                            <div class="space-y-3">
                                <button class="quick-action-btn w-full flex items-center justify-between p-3 bg-gym-primary/10 hover:bg-gym-primary/20 rounded-lg transition-all duration-300 group">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-user-plus text-gym-primary"></i>
                                        <span style="color: var(--text-primary);">Add New Member</span>
                                    </div>
                                    <i class="fas fa-arrow-right group-hover:text-gym-primary transition-colors" style="color: var(--text-muted);"></i>
                                </button>

                                <button class="quick-action-btn w-full flex items-center justify-between p-3 bg-gym-secondary/10 hover:bg-gym-secondary/20 rounded-lg transition-all duration-300 group">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-sign-in-alt text-gym-secondary"></i>
                                        <span style="color: var(--text-primary);">Quick Check-in</span>
                                    </div>
                                    <i class="fas fa-arrow-right group-hover:text-gym-secondary transition-colors" style="color: var(--text-muted);"></i>
                                </button>

                                <button class="quick-action-btn w-full flex items-center justify-between p-3 bg-gym-accent/10 hover:bg-gym-accent/20 rounded-lg transition-all duration-300 group">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-calendar-plus text-gym-accent"></i>
                                        <span style="color: var(--text-primary);">Schedule Class</span>
                                    </div>
                                    <i class="fas fa-arrow-right group-hover:text-gym-accent transition-colors" style="color: var(--text-muted);"></i>
                                </button>

                                <button class="quick-action-btn w-full flex items-center justify-between p-3 bg-gym-blue/10 hover:bg-gym-blue/20 rounded-lg transition-all duration-300 group">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-chart-line text-gym-blue"></i>
                                        <span style="color: var(--text-primary);">View Reports</span>
                                    </div>
                                    <i class="fas fa-arrow-right group-hover:text-gym-blue transition-colors" style="color: var(--text-muted);"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="lg:col-span-2">
                        <div class="rounded-xl p-6" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Recent Activity</h3>
                                <button class="text-gym-primary hover:text-gym-primary/80 text-sm font-medium">View All</button>
                            </div>
                            <div class="space-y-4">
                                <div class="activity-card flex items-center space-x-4 p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="w-10 h-10 bg-gym-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-sign-in-alt text-white text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium" style="color: var(--text-primary);">John Smith checked in</p>
                                        <p class="text-sm" style="color: var(--text-muted);">Premium member • 2 minutes ago</p>
                                    </div>
                                    <span class="text-gym-primary text-sm font-medium">Active</span>
                                </div>

                                <div class="activity-card flex items-center space-x-4 p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="w-10 h-10 bg-gym-secondary rounded-full flex items-center justify-center">
                                        <i class="fas fa-user-plus text-white text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium" style="color: var(--text-primary);">Sarah Johnson registered</p>
                                        <p class="text-sm" style="color: var(--text-muted);">Basic membership • 15 minutes ago</p>
                                    </div>
                                    <span class="text-gym-secondary text-sm font-medium">New</span>
                                </div>

                                <div class="activity-card flex items-center space-x-4 p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="w-10 h-10 bg-gym-accent rounded-full flex items-center justify-center">
                                        <i class="fas fa-credit-card text-white text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium" style="color: var(--text-primary);">Payment received from Mike Davis</p>
                                        <p class="text-sm" style="color: var(--text-muted);">$89.99 • 1 hour ago</p>
                                    </div>
                                    <span class="text-gym-accent text-sm font-medium">Paid</span>
                                </div>

                                <div class="activity-card flex items-center space-x-4 p-3 rounded-lg" style="background-color: var(--hover-bg);">
                                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-dumbbell text-white text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium" style="color: var(--text-primary);">HIIT class completed</p>
                                        <p class="text-sm" style="color: var(--text-muted);">18 attendees • 2 hours ago</p>
                                    </div>
                                    <span class="text-green-400 text-sm font-medium">Complete</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Member Profile Section -->
            <div id="member-profile-section" class="section hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Profile Info -->
                    <div class="lg:col-span-1">
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                            <div class="text-center">
                                <div class="w-24 h-24 bg-gym-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                                    <i class="fas fa-user text-white text-2xl"></i>
                                </div>
                                <h2 class="text-xl font-bold text-white">John Smith</h2>
                                <p class="text-gray-400">Premium Member</p>
                                <div class="flex items-center justify-center mt-2">
                                    <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                                </div>
                            </div>

                            <div class="mt-6 space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Member ID</span>
                                    <span class="text-white">#12345</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Join Date</span>
                                    <span class="text-white">Jan 15, 2024</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Phone</span>
                                    <span class="text-white">(*************</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Email</span>
                                    <span class="text-white text-sm"><EMAIL></span>
                                </div>
                            </div>

                            <div class="mt-6 pt-6 border-t border-gray-800">
                                <button class="w-full bg-gym-primary hover:bg-gym-primary/80 text-white py-2 px-4 rounded-lg transition-colors">
                                    Edit Profile
                                </button>
                            </div>
                        </div>

                        <!-- Membership Status -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                            <h3 class="text-lg font-semibold text-white mb-4">Membership Status</h3>
                            <div class="space-y-4">
                                <div>
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gray-400">Plan</span>
                                        <span class="text-gym-primary font-medium">Premium</span>
                                    </div>
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gray-400">Expires</span>
                                        <span class="text-white">Dec 15, 2024</span>
                                    </div>
                                    <div class="w-full bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-primary h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <p class="text-xs text-gray-400 mt-1">9 months remaining</p>
                                </div>

                                <div class="pt-4 border-t border-gray-800">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gray-400">Monthly Fee</span>
                                        <span class="text-white font-medium">$89.99</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Next Payment</span>
                                        <span class="text-white">Mar 15, 2024</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activity & Progress -->
                    <div class="lg:col-span-2">
                        <!-- Progress Stats -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-gray-400 text-sm">This Month</p>
                                        <p class="text-2xl font-bold text-white">18</p>
                                        <p class="text-gym-primary text-sm">Visits</p>
                                    </div>
                                    <i class="fas fa-calendar-check text-gym-primary text-2xl"></i>
                                </div>
                            </div>

                            <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-gray-400 text-sm">Total Hours</p>
                                        <p class="text-2xl font-bold text-white">47</p>
                                        <p class="text-gym-secondary text-sm">This Month</p>
                                    </div>
                                    <i class="fas fa-clock text-gym-secondary text-2xl"></i>
                                </div>
                            </div>

                            <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-gray-400 text-sm">Classes</p>
                                        <p class="text-2xl font-bold text-white">12</p>
                                        <p class="text-gym-accent text-sm">Attended</p>
                                    </div>
                                    <i class="fas fa-users text-gym-accent text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Visits -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Recent Visits</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gym-primary rounded-full flex items-center justify-center">
                                            <i class="fas fa-sign-in-alt text-white text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-white font-medium">Today</p>
                                            <p class="text-gray-400 text-sm">6:30 AM - 8:15 AM</p>
                                        </div>
                                    </div>
                                    <span class="text-gym-primary text-sm">1h 45m</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                            <i class="fas fa-sign-out-alt text-white text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-white font-medium">Yesterday</p>
                                            <p class="text-gray-400 text-sm">7:00 PM - 8:30 PM</p>
                                        </div>
                                    </div>
                                    <span class="text-gray-400 text-sm">1h 30m</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                            <i class="fas fa-sign-out-alt text-white text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-white font-medium">Mar 10</p>
                                            <p class="text-gray-400 text-sm">5:45 AM - 7:00 AM</p>
                                        </div>
                                    </div>
                                    <span class="text-gray-400 text-sm">1h 15m</span>
                                </div>
                            </div>
                        </div>

                        <!-- Goals & Achievements -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                            <h3 class="text-lg font-semibold text-white mb-4">Goals & Achievements</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="p-4 bg-gym-primary/10 rounded-lg border border-gym-primary/20">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gym-primary font-medium">Monthly Visits</span>
                                        <i class="fas fa-trophy text-gym-primary"></i>
                                    </div>
                                    <div class="w-full bg-gray-800 rounded-full h-2 mb-2">
                                        <div class="bg-gym-primary h-2 rounded-full" style="width: 90%"></div>
                                    </div>
                                    <p class="text-sm text-gray-400">18/20 visits</p>
                                </div>

                                <div class="p-4 bg-gym-secondary/10 rounded-lg border border-gym-secondary/20">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gym-secondary font-medium">Workout Hours</span>
                                        <i class="fas fa-clock text-gym-secondary"></i>
                                    </div>
                                    <div class="w-full bg-gray-800 rounded-full h-2 mb-2">
                                        <div class="bg-gym-secondary h-2 rounded-full" style="width: 78%"></div>
                                    </div>
                                    <p class="text-sm text-gray-400">47/60 hours</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Member Registration Section -->
            <div id="registration-section" class="section hidden">
                <div class="max-w-4xl mx-auto">
                    <div class="bg-gym-dark rounded-xl p-8 border border-gray-800">
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-white mb-2">New Member Registration</h2>
                            <p class="text-gray-400">Fill out the form below to register a new gym member</p>
                        </div>

                        <form class="space-y-8">
                            <!-- Personal Information -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-user mr-2 text-gym-primary"></i>
                                    Personal Information
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">First Name *</label>
                                        <input type="text" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter first name">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Last Name *</label>
                                        <input type="text" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter last name">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                                        <input type="email" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter email address">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Phone Number *</label>
                                        <input type="tel" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="(*************">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Date of Birth *</label>
                                        <input type="date" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Gender</label>
                                        <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                            <option value="">Select gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                            <option value="prefer-not-to-say">Prefer not to say</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-map-marker-alt mr-2 text-gym-secondary"></i>
                                    Address Information
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Street Address</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter street address">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">City</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter city">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">State</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter state">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">ZIP Code</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter ZIP code">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Country</label>
                                        <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                            <option value="us">United States</option>
                                            <option value="ca">Canada</option>
                                            <option value="uk">United Kingdom</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Emergency Contact -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-phone mr-2 text-red-400"></i>
                                    Emergency Contact
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Contact Name</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter contact name">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Contact Phone</label>
                                        <input type="tel"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="(*************">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Relationship</label>
                                        <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                            <option value="">Select relationship</option>
                                            <option value="spouse">Spouse</option>
                                            <option value="parent">Parent</option>
                                            <option value="sibling">Sibling</option>
                                            <option value="friend">Friend</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Membership Plan Selection -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-credit-card mr-2 text-gym-accent"></i>
                                    Membership Plan
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div class="relative">
                                        <input type="radio" id="basic" name="membership" value="basic" class="sr-only peer">
                                        <label for="basic" class="block p-6 bg-gray-800 border-2 border-gray-700 rounded-xl cursor-pointer hover:border-gym-primary peer-checked:border-gym-primary peer-checked:bg-gym-primary/10 transition-all">
                                            <div class="text-center">
                                                <h4 class="text-lg font-semibold text-white mb-2">Basic</h4>
                                                <div class="text-3xl font-bold text-gym-primary mb-2">$29<span class="text-sm text-gray-400">/mo</span></div>
                                                <ul class="text-sm text-gray-300 space-y-2">
                                                    <li>• Gym access</li>
                                                    <li>• Basic equipment</li>
                                                    <li>• Locker room</li>
                                                </ul>
                                            </div>
                                        </label>
                                    </div>

                                    <div class="relative">
                                        <input type="radio" id="premium" name="membership" value="premium" class="sr-only peer">
                                        <label for="premium" class="block p-6 bg-gray-800 border-2 border-gray-700 rounded-xl cursor-pointer hover:border-gym-primary peer-checked:border-gym-primary peer-checked:bg-gym-primary/10 transition-all">
                                            <div class="text-center">
                                                <h4 class="text-lg font-semibold text-white mb-2">Premium</h4>
                                                <div class="text-3xl font-bold text-gym-primary mb-2">$59<span class="text-sm text-gray-400">/mo</span></div>
                                                <ul class="text-sm text-gray-300 space-y-2">
                                                    <li>• All Basic features</li>
                                                    <li>• Group classes</li>
                                                    <li>• Personal trainer</li>
                                                    <li>• Nutrition guidance</li>
                                                </ul>
                                            </div>
                                        </label>
                                    </div>

                                    <div class="relative">
                                        <input type="radio" id="elite" name="membership" value="elite" class="sr-only peer">
                                        <label for="elite" class="block p-6 bg-gray-800 border-2 border-gray-700 rounded-xl cursor-pointer hover:border-gym-primary peer-checked:border-gym-primary peer-checked:bg-gym-primary/10 transition-all">
                                            <div class="text-center">
                                                <h4 class="text-lg font-semibold text-white mb-2">Elite</h4>
                                                <div class="text-3xl font-bold text-gym-primary mb-2">$99<span class="text-sm text-gray-400">/mo</span></div>
                                                <ul class="text-sm text-gray-300 space-y-2">
                                                    <li>• All Premium features</li>
                                                    <li>• 24/7 access</li>
                                                    <li>• Guest privileges</li>
                                                    <li>• Spa access</li>
                                                </ul>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Health Information -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-heartbeat mr-2 text-red-400"></i>
                                    Health Information
                                </h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Medical Conditions</label>
                                        <textarea rows="3"
                                                  class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                                  placeholder="List any medical conditions or concerns..."></textarea>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-300 mb-2">Fitness Goals</label>
                                            <select multiple class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent h-32">
                                                <option value="weight-loss">Weight Loss</option>
                                                <option value="muscle-gain">Muscle Gain</option>
                                                <option value="endurance">Endurance</option>
                                                <option value="strength">Strength Training</option>
                                                <option value="flexibility">Flexibility</option>
                                                <option value="general-fitness">General Fitness</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-300 mb-2">Experience Level</label>
                                            <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                                <option value="">Select experience level</option>
                                                <option value="beginner">Beginner</option>
                                                <option value="intermediate">Intermediate</option>
                                                <option value="advanced">Advanced</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Terms and Conditions -->
                            <div>
                                <div class="bg-gray-800/50 rounded-lg p-6">
                                    <div class="space-y-4">
                                        <div class="flex items-start space-x-3">
                                            <input type="checkbox" id="terms" required
                                                   class="mt-1 w-4 h-4 text-gym-primary bg-gray-800 border-gray-600 rounded focus:ring-gym-primary focus:ring-2">
                                            <label for="terms" class="text-sm text-gray-300">
                                                I agree to the <a href="#" class="text-gym-primary hover:underline">Terms and Conditions</a> and <a href="#" class="text-gym-primary hover:underline">Privacy Policy</a>
                                            </label>
                                        </div>
                                        <div class="flex items-start space-x-3">
                                            <input type="checkbox" id="waiver" required
                                                   class="mt-1 w-4 h-4 text-gym-primary bg-gray-800 border-gray-600 rounded focus:ring-gym-primary focus:ring-2">
                                            <label for="waiver" class="text-sm text-gray-300">
                                                I acknowledge that I have read and understood the liability waiver
                                            </label>
                                        </div>
                                        <div class="flex items-start space-x-3">
                                            <input type="checkbox" id="marketing"
                                                   class="mt-1 w-4 h-4 text-gym-primary bg-gray-800 border-gray-600 rounded focus:ring-gym-primary focus:ring-2">
                                            <label for="marketing" class="text-sm text-gray-300">
                                                I would like to receive promotional emails and updates
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-800">
                                <button type="button" class="flex-1 bg-gym-blue/20 hover:bg-gym-blue/30 text-gym-blue border border-gym-blue/30 py-3 px-6 rounded-lg transition-colors">
                                    Save as Draft
                                </button>
                                <button type="submit" class="flex-1 bg-gym-primary hover:bg-gym-primary/80 text-white py-3 px-6 rounded-lg transition-colors font-medium">
                                    Register Member
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Members Directory Section -->
            <div id="members-section" class="section hidden">
                <!-- Search and Filters -->
                <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <div class="flex-1 max-w-md">
                            <div class="relative">
                                <input type="text" placeholder="Search members..."
                                       class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 pl-10 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <div class="flex flex-wrap gap-3">
                            <select class="bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="suspended">Suspended</option>
                            </select>

                            <select class="bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                <option value="">All Plans</option>
                                <option value="basic">Basic</option>
                                <option value="premium">Premium</option>
                                <option value="elite">Elite</option>
                            </select>

                            <button class="bg-gym-primary hover:bg-gym-primary/80 text-white px-4 py-3 rounded-lg transition-colors">
                                <i class="fas fa-filter mr-2"></i>Filter
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Members Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-6">
                    <!-- Member Card 1 -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 hover:border-gym-primary/50 transition-colors">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-1">John Smith</h3>
                        <p class="text-gray-400 text-sm mb-3">Premium Member</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">ID:</span>
                                <span class="text-white">#12345</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Joined:</span>
                                <span class="text-white">Jan 2024</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Last Visit:</span>
                                <span class="text-gym-primary">Today</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-800 flex gap-2">
                            <button class="flex-1 bg-gym-primary/20 hover:bg-gym-primary/30 text-gym-primary py-2 px-3 rounded-lg text-sm transition-colors">
                                View
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors">
                                Edit
                            </button>
                        </div>
                    </div>

                    <!-- Member Card 2 -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 hover:border-gym-primary/50 transition-colors">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-secondary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="bg-gym-secondary text-white text-xs px-2 py-1 rounded-full">Active</span>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-1">Sarah Johnson</h3>
                        <p class="text-gray-400 text-sm mb-3">Basic Member</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">ID:</span>
                                <span class="text-white">#12346</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Joined:</span>
                                <span class="text-white">Feb 2024</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Last Visit:</span>
                                <span class="text-white">Yesterday</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-800 flex gap-2">
                            <button class="flex-1 bg-gym-primary/20 hover:bg-gym-primary/30 text-gym-primary py-2 px-3 rounded-lg text-sm transition-colors">
                                View
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors">
                                Edit
                            </button>
                        </div>
                    </div>

                    <!-- Member Card 3 -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 hover:border-gym-blue/50 transition-colors">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-blue rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="bg-gym-blue text-white text-xs px-2 py-1 rounded-full">Active</span>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-1">Mike Davis</h3>
                        <p class="text-gray-400 text-sm mb-3">Elite Member</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">ID:</span>
                                <span class="text-white">#12347</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Joined:</span>
                                <span class="text-white">Dec 2023</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Last Visit:</span>
                                <span class="text-white">2 days ago</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-800 flex gap-2">
                            <button class="flex-1 bg-gym-blue/20 hover:bg-gym-blue/30 text-gym-blue py-2 px-3 rounded-lg text-sm transition-colors">
                                View
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors">
                                Edit
                            </button>
                        </div>
                    </div>

                    <!-- Member Card 4 -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 hover:border-gym-primary/50 transition-colors">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="bg-gray-600 text-white text-xs px-2 py-1 rounded-full">Inactive</span>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-1">Lisa Wilson</h3>
                        <p class="text-gray-400 text-sm mb-3">Premium Member</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">ID:</span>
                                <span class="text-white">#12348</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Joined:</span>
                                <span class="text-white">Nov 2023</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Last Visit:</span>
                                <span class="text-gray-500">2 weeks ago</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-800 flex gap-2">
                            <button class="flex-1 bg-gym-primary/20 hover:bg-gym-primary/30 text-gym-primary py-2 px-3 rounded-lg text-sm transition-colors">
                                View
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors">
                                Edit
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-400">
                            Showing 1-12 of 1,247 members
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-2 bg-gym-primary text-white rounded-lg">1</button>
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">2</button>
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">3</button>
                            <span class="px-3 py-2 text-gray-400">...</span>
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">104</button>
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Membership Plans Section -->
            <div id="memberships-section" class="section hidden">
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">Membership Plans</h2>
                    <p class="text-gray-400">Choose the perfect plan for your fitness journey</p>
                </div>

                <!-- Pricing Cards -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                    <!-- Basic Plan -->
                    <div class="bg-gym-dark rounded-2xl p-8 border border-gray-800 hover:border-gym-primary/50 transition-all duration-300">
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-gym-secondary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-dumbbell text-gym-secondary text-2xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-white mb-2">Basic</h3>
                            <p class="text-gray-400 mb-6">Perfect for getting started</p>
                            <div class="mb-6">
                                <span class="text-5xl font-bold text-white">$29</span>
                                <span class="text-gray-400">/month</span>
                            </div>
                        </div>

                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-secondary mr-3"></i>
                                Access to gym equipment
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-secondary mr-3"></i>
                                Locker room access
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-secondary mr-3"></i>
                                Basic workout tracking
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-secondary mr-3"></i>
                                Mobile app access
                            </li>
                            <li class="flex items-center text-gray-500">
                                <i class="fas fa-times text-gray-600 mr-3"></i>
                                Group fitness classes
                            </li>
                            <li class="flex items-center text-gray-500">
                                <i class="fas fa-times text-gray-600 mr-3"></i>
                                Personal training
                            </li>
                        </ul>

                        <button class="w-full bg-gym-secondary hover:bg-gym-secondary/80 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                            Choose Basic
                        </button>
                    </div>

                    <!-- Premium Plan -->
                    <div class="bg-gym-dark rounded-2xl p-8 border-2 border-gym-primary relative hover:border-gym-primary/80 transition-all duration-300 transform hover:scale-105">
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                            <span class="bg-gym-primary text-white px-4 py-2 rounded-full text-sm font-medium">Most Popular</span>
                        </div>

                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-gym-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-fire text-gym-primary text-2xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-white mb-2">Premium</h3>
                            <p class="text-gray-400 mb-6">For serious fitness enthusiasts</p>
                            <div class="mb-6">
                                <span class="text-5xl font-bold text-white">$59</span>
                                <span class="text-gray-400">/month</span>
                            </div>
                        </div>

                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                Everything in Basic
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                Unlimited group classes
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                2 personal training sessions
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                Nutrition consultation
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                Priority booking
                            </li>
                            <li class="flex items-center text-gray-500">
                                <i class="fas fa-times text-gray-600 mr-3"></i>
                                24/7 gym access
                            </li>
                        </ul>

                        <button class="w-full bg-gym-primary hover:bg-gym-primary/80 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                            Choose Premium
                        </button>
                    </div>

                    <!-- Elite Plan -->
                    <div class="bg-gym-dark rounded-2xl p-8 border border-gray-800 hover:border-gym-blue/50 transition-all duration-300">
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-gym-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-crown text-gym-blue text-2xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-white mb-2">Elite</h3>
                            <p class="text-gray-400 mb-6">Ultimate fitness experience</p>
                            <div class="mb-6">
                                <span class="text-5xl font-bold text-white">$99</span>
                                <span class="text-gray-400">/month</span>
                            </div>
                        </div>

                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Everything in Premium
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                24/7 gym access
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Unlimited personal training
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Guest privileges (2 per month)
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Spa & sauna access
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Exclusive member events
                            </li>
                        </ul>

                        <button class="w-full bg-gym-blue hover:bg-gym-blue/80 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                            Choose Elite
                        </button>
                    </div>
                </div>

                <!-- Plan Comparison Table -->
                <div class="bg-gym-dark rounded-xl border border-gray-800 overflow-hidden">
                    <div class="p-6 border-b border-gray-800">
                        <h3 class="text-xl font-bold text-white">Plan Comparison</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-800/50">
                                <tr>
                                    <th class="text-left p-4 text-gray-300 font-medium">Features</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Basic</th>
                                    <th class="text-center p-4 text-gym-primary font-medium">Premium</th>
                                    <th class="text-center p-4 text-gym-blue font-medium">Elite</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-800">
                                <tr>
                                    <td class="p-4 text-gray-300">Gym Equipment Access</td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-secondary"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-primary"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-blue"></i></td>
                                </tr>
                                <tr class="bg-gray-800/25">
                                    <td class="p-4 text-gray-300">Group Fitness Classes</td>
                                    <td class="p-4 text-center"><i class="fas fa-times text-gray-600"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-primary"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-blue"></i></td>
                                </tr>
                                <tr>
                                    <td class="p-4 text-gray-300">Personal Training Sessions</td>
                                    <td class="p-4 text-center text-gray-600">0</td>
                                    <td class="p-4 text-center text-gym-primary">2/month</td>
                                    <td class="p-4 text-center text-gym-blue">Unlimited</td>
                                </tr>
                                <tr class="bg-gray-800/25">
                                    <td class="p-4 text-gray-300">24/7 Access</td>
                                    <td class="p-4 text-center"><i class="fas fa-times text-gray-600"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-times text-gray-600"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-blue"></i></td>
                                </tr>
                                <tr>
                                    <td class="p-4 text-gray-300">Guest Privileges</td>
                                    <td class="p-4 text-center text-gray-600">0</td>
                                    <td class="p-4 text-center text-gray-600">0</td>
                                    <td class="p-4 text-center text-gym-blue">2/month</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Check-in/Check-out Section -->
            <div id="checkin-section" class="section hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Quick Check-in -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6 flex items-center">
                            <i class="fas fa-sign-in-alt mr-3 text-gym-primary"></i>
                            Quick Check-in
                        </h3>

                        <div class="space-y-4">
                            <div class="relative">
                                <input type="text" placeholder="Enter Member ID or scan card..."
                                       class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-4 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent text-lg">
                                <i class="fas fa-id-card absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <button class="bg-gym-primary hover:bg-gym-primary/80 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                                    <i class="fas fa-sign-in-alt mr-2"></i>Check In
                                </button>
                                <button class="bg-gym-secondary hover:bg-gym-secondary/80 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Check Out
                                </button>
                            </div>
                        </div>

                        <!-- Member Info Display -->
                        <div id="member-info" class="mt-6 p-4 bg-gray-800/50 rounded-lg hidden">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-gym-primary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div>
                                    <h4 class="text-white font-medium">John Smith</h4>
                                    <p class="text-gray-400 text-sm">Premium Member • ID: #12345</p>
                                </div>
                                <div class="ml-auto">
                                    <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Status -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6 flex items-center">
                            <i class="fas fa-users mr-3 text-gym-secondary"></i>
                            Current Status
                        </h3>

                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="bg-gym-primary/10 rounded-lg p-4 border border-gym-primary/20">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gym-primary">89</div>
                                    <div class="text-sm text-gray-400">Currently In</div>
                                </div>
                            </div>
                            <div class="bg-gym-secondary/10 rounded-lg p-4 border border-gym-secondary/20">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gym-secondary">156</div>
                                    <div class="text-sm text-gray-400">Today's Total</div>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-400">Peak Hours</span>
                                <span class="text-white">6-8 AM, 6-8 PM</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-400">Average Stay</span>
                                <span class="text-white">1h 45m</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-400">Capacity</span>
                                <span class="text-gym-primary">59% (89/150)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Check-ins -->
                <div class="mt-8 bg-gym-dark rounded-xl p-6 border border-gray-800">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-white">Recent Check-ins</h3>
                        <button class="text-gym-primary hover:text-gym-primary/80 text-sm font-medium">View All</button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-800">
                                    <th class="text-left py-3 text-gray-400 font-medium">Member</th>
                                    <th class="text-left py-3 text-gray-400 font-medium">Plan</th>
                                    <th class="text-left py-3 text-gray-400 font-medium">Check-in Time</th>
                                    <th class="text-left py-3 text-gray-400 font-medium">Status</th>
                                    <th class="text-left py-3 text-gray-400 font-medium">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-800">
                                <tr>
                                    <td class="py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-gym-primary rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-white text-xs"></i>
                                            </div>
                                            <div>
                                                <div class="text-white font-medium">John Smith</div>
                                                <div class="text-gray-400 text-sm">#12345</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4 text-gray-300">Premium</td>
                                    <td class="py-4 text-gray-300">2 minutes ago</td>
                                    <td class="py-4">
                                        <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                                    </td>
                                    <td class="py-4">
                                        <button class="text-gym-secondary hover:text-gym-secondary/80 text-sm">Check Out</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-gym-secondary rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-white text-xs"></i>
                                            </div>
                                            <div>
                                                <div class="text-white font-medium">Sarah Johnson</div>
                                                <div class="text-gray-400 text-sm">#12346</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4 text-gray-300">Basic</td>
                                    <td class="py-4 text-gray-300">15 minutes ago</td>
                                    <td class="py-4">
                                        <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                                    </td>
                                    <td class="py-4">
                                        <button class="text-gym-secondary hover:text-gym-secondary/80 text-sm">Check Out</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-white text-xs"></i>
                                            </div>
                                            <div>
                                                <div class="text-white font-medium">Mike Davis</div>
                                                <div class="text-gray-400 text-sm">#12347</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4 text-gray-300">Elite</td>
                                    <td class="py-4 text-gray-300">1 hour ago</td>
                                    <td class="py-4">
                                        <span class="bg-gray-600 text-white text-xs px-2 py-1 rounded-full">Checked Out</span>
                                    </td>
                                    <td class="py-4">
                                        <span class="text-gray-500 text-sm">1h 30m visit</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Billing Section -->
            <div id="billing-section" class="section hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Payment Overview -->
                    <div class="lg:col-span-2">
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                            <h3 class="text-xl font-bold text-white mb-6">Payment History</h3>

                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead>
                                        <tr class="border-b border-gray-800">
                                            <th class="text-left py-3 text-gray-400 font-medium">Date</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Member</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Plan</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Amount</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Status</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Method</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-800">
                                        <tr>
                                            <td class="py-4 text-gray-300">Mar 12, 2024</td>
                                            <td class="py-4">
                                                <div class="text-white font-medium">John Smith</div>
                                                <div class="text-gray-400 text-sm">#12345</div>
                                            </td>
                                            <td class="py-4 text-gray-300">Premium</td>
                                            <td class="py-4 text-white font-medium">$59.99</td>
                                            <td class="py-4">
                                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">Paid</span>
                                            </td>
                                            <td class="py-4 text-gray-300">Credit Card</td>
                                        </tr>
                                        <tr>
                                            <td class="py-4 text-gray-300">Mar 11, 2024</td>
                                            <td class="py-4">
                                                <div class="text-white font-medium">Sarah Johnson</div>
                                                <div class="text-gray-400 text-sm">#12346</div>
                                            </td>
                                            <td class="py-4 text-gray-300">Basic</td>
                                            <td class="py-4 text-white font-medium">$29.99</td>
                                            <td class="py-4">
                                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">Paid</span>
                                            </td>
                                            <td class="py-4 text-gray-300">Bank Transfer</td>
                                        </tr>
                                        <tr>
                                            <td class="py-4 text-gray-300">Mar 10, 2024</td>
                                            <td class="py-4">
                                                <div class="text-white font-medium">Mike Davis</div>
                                                <div class="text-gray-400 text-sm">#12347</div>
                                            </td>
                                            <td class="py-4 text-gray-300">Elite</td>
                                            <td class="py-4 text-white font-medium">$99.99</td>
                                            <td class="py-4">
                                                <span class="bg-gym-secondary text-white text-xs px-2 py-1 rounded-full">Pending</span>
                                            </td>
                                            <td class="py-4 text-gray-300">Credit Card</td>
                                        </tr>
                                        <tr>
                                            <td class="py-4 text-gray-300">Mar 09, 2024</td>
                                            <td class="py-4">
                                                <div class="text-white font-medium">Lisa Wilson</div>
                                                <div class="text-gray-400 text-sm">#12348</div>
                                            </td>
                                            <td class="py-4 text-gray-300">Premium</td>
                                            <td class="py-4 text-white font-medium">$59.99</td>
                                            <td class="py-4">
                                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">Failed</span>
                                            </td>
                                            <td class="py-4 text-gray-300">Credit Card</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Outstanding Payments -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                            <h3 class="text-xl font-bold text-white mb-6">Outstanding Payments</h3>

                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-exclamation text-white"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">Lisa Wilson - Premium</div>
                                            <div class="text-gray-400 text-sm">Payment failed • Due: Mar 09, 2024</div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-white font-bold">$59.99</div>
                                        <button class="text-gym-primary hover:text-gym-primary/80 text-sm">Retry Payment</button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-gym-secondary/10 border border-gym-secondary/20 rounded-lg">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-10 h-10 bg-gym-secondary rounded-full flex items-center justify-center">
                                            <i class="fas fa-clock text-white"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">Mike Davis - Elite</div>
                                            <div class="text-gray-400 text-sm">Payment processing • Due: Mar 10, 2024</div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-white font-bold">$99.99</div>
                                        <span class="text-gym-secondary text-sm">Processing</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Revenue Summary -->
                    <div class="lg:col-span-1">
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Revenue Summary</h3>

                            <div class="space-y-4">
                                <div class="p-4 bg-gym-primary/10 rounded-lg border border-gym-primary/20">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gym-primary">$24,580</div>
                                        <div class="text-sm text-gray-400">This Month</div>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Basic Plans</span>
                                        <span class="text-white">$8,970</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Premium Plans</span>
                                        <span class="text-white">$11,980</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Elite Plans</span>
                                        <span class="text-white">$3,630</span>
                                    </div>
                                </div>

                                <div class="pt-4 border-t border-gray-800">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Outstanding</span>
                                        <span class="text-red-400">$159.98</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                            <h3 class="text-lg font-semibold text-white mb-4">Payment Methods</h3>

                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-credit-card text-gym-primary"></i>
                                        <span class="text-white">Credit Cards</span>
                                    </div>
                                    <span class="text-gray-400">78%</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-university text-gym-secondary"></i>
                                        <span class="text-white">Bank Transfer</span>
                                    </div>
                                    <span class="text-gray-400">15%</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-money-bill text-gym-accent"></i>
                                        <span class="text-white">Cash</span>
                                    </div>
                                    <span class="text-gray-400">7%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Class Schedule Section -->
            <div id="schedule-section" class="section hidden">
                <div class="mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h2 class="text-2xl font-bold text-white mb-2">Class Schedule</h2>
                            <p class="text-gray-400">Manage fitness classes and bookings</p>
                        </div>
                        <div class="flex gap-3">
                            <button class="bg-gym-primary hover:bg-gym-primary/80 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i>Add Class
                            </button>
                            <select class="bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary">
                                <option>This Week</option>
                                <option>Next Week</option>
                                <option>This Month</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Weekly Schedule -->
                <div class="bg-gym-dark rounded-xl border border-gray-800 overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full min-w-[800px]">
                            <thead class="bg-gray-800/50">
                                <tr>
                                    <th class="text-left p-4 text-gray-300 font-medium w-24">Time</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Monday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Tuesday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Wednesday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Thursday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Friday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Saturday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Sunday</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-800">
                                <tr>
                                    <td class="p-4 text-gray-400 font-medium">6:00 AM</td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Morning Yoga</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">12/15</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Morning Yoga</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">10/15</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Morning Yoga</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">8/15</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                    <td class="p-2"></td>
                                </tr>
                                <tr>
                                    <td class="p-4 text-gray-400 font-medium">7:00 AM</td>
                                    <td class="p-2">
                                        <div class="bg-gym-secondary/20 border border-gym-secondary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-secondary font-medium text-sm">HIIT Training</div>
                                            <div class="text-gray-400 text-xs">Mike R.</div>
                                            <div class="text-gray-400 text-xs">20/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-accent/20 border border-gym-accent/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-accent font-medium text-sm">Pilates</div>
                                            <div class="text-gray-400 text-xs">Emma L.</div>
                                            <div class="text-gray-400 text-xs">14/18</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-secondary/20 border border-gym-secondary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-secondary font-medium text-sm">HIIT Training</div>
                                            <div class="text-gray-400 text-xs">Mike R.</div>
                                            <div class="text-gray-400 text-xs">18/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-accent/20 border border-gym-accent/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-accent font-medium text-sm">Pilates</div>
                                            <div class="text-gray-400 text-xs">Emma L.</div>
                                            <div class="text-gray-400 text-xs">16/18</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-secondary/20 border border-gym-secondary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-secondary font-medium text-sm">HIIT Training</div>
                                            <div class="text-gray-400 text-xs">Mike R.</div>
                                            <div class="text-gray-400 text-xs">15/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-purple-500/20 border border-purple-500/30 rounded-lg p-3 text-center">
                                            <div class="text-purple-400 font-medium text-sm">Zumba</div>
                                            <div class="text-gray-400 text-xs">Lisa M.</div>
                                            <div class="text-gray-400 text-xs">22/25</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                </tr>
                                <tr>
                                    <td class="p-4 text-gray-400 font-medium">6:00 PM</td>
                                    <td class="p-2">
                                        <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-center">
                                            <div class="text-red-400 font-medium text-sm">CrossFit</div>
                                            <div class="text-gray-400 text-xs">John D.</div>
                                            <div class="text-gray-400 text-xs">12/12</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Yoga Flow</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">18/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-center">
                                            <div class="text-red-400 font-medium text-sm">CrossFit</div>
                                            <div class="text-gray-400 text-xs">John D.</div>
                                            <div class="text-gray-400 text-xs">11/12</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Yoga Flow</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">15/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-center">
                                            <div class="text-red-400 font-medium text-sm">CrossFit</div>
                                            <div class="text-gray-400 text-xs">John D.</div>
                                            <div class="text-gray-400 text-xs">12/12</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                    <td class="p-2">
                                        <div class="bg-gym-accent/20 border border-gym-accent/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-accent font-medium text-sm">Meditation</div>
                                            <div class="text-gray-400 text-xs">Anna P.</div>
                                            <div class="text-gray-400 text-xs">8/15</div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Class Details -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
                    <!-- Popular Classes -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6">Popular Classes</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gym-secondary rounded-full flex items-center justify-center">
                                        <i class="fas fa-fire text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-white font-medium">HIIT Training</div>
                                        <div class="text-gray-400 text-sm">High intensity workout</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-gym-secondary font-bold">95%</div>
                                    <div class="text-gray-400 text-sm">Avg. Full</div>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-music text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-white font-medium">Zumba</div>
                                        <div class="text-gray-400 text-sm">Dance fitness class</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-purple-400 font-bold">88%</div>
                                    <div class="text-gray-400 text-sm">Avg. Full</div>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gym-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-leaf text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-white font-medium">Yoga Flow</div>
                                        <div class="text-gray-400 text-sm">Mindful movement</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-gym-primary font-bold">82%</div>
                                    <div class="text-gray-400 text-sm">Avg. Full</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instructors -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6">Top Instructors</h3>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg">
                                <div class="w-12 h-12 bg-gym-primary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="text-white font-medium">Mike Rodriguez</div>
                                    <div class="text-gray-400 text-sm">HIIT & CrossFit Specialist</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-gym-primary">4.9★</div>
                                    <div class="text-gray-400 text-sm">156 reviews</div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg">
                                <div class="w-12 h-12 bg-gym-secondary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="text-white font-medium">Sarah Kim</div>
                                    <div class="text-gray-400 text-sm">Yoga & Meditation</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-gym-secondary">4.8★</div>
                                    <div class="text-gray-400 text-sm">203 reviews</div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg">
                                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="text-white font-medium">Lisa Martinez</div>
                                    <div class="text-gray-400 text-sm">Zumba & Dance Fitness</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-purple-400">4.9★</div>
                                    <div class="text-gray-400 text-sm">189 reviews</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div id="statistics-section" class="section hidden">
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">Statistics & Analytics</h2>
                    <p class="text-gray-400">Track gym performance and member engagement</p>
                </div>

                <!-- Key Metrics -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-primary/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-gym-primary text-xl"></i>
                            </div>
                            <span class="text-gym-primary text-sm">+12%</span>
                        </div>
                        <div class="text-3xl font-bold text-white mb-1">1,247</div>
                        <div class="text-gray-400 text-sm">Total Members</div>
                        <div class="w-full bg-gray-800 rounded-full h-2 mt-3">
                            <div class="bg-gym-primary h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>

                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-secondary/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-gym-secondary text-xl"></i>
                            </div>
                            <span class="text-gym-secondary text-sm">+8%</span>
                        </div>
                        <div class="text-3xl font-bold text-white mb-1">89%</div>
                        <div class="text-gray-400 text-sm">Retention Rate</div>
                        <div class="w-full bg-gray-800 rounded-full h-2 mt-3">
                            <div class="bg-gym-secondary h-2 rounded-full" style="width: 89%"></div>
                        </div>
                    </div>

                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-accent/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-calendar-check text-gym-accent text-xl"></i>
                            </div>
                            <span class="text-gym-accent text-sm">+15%</span>
                        </div>
                        <div class="text-3xl font-bold text-white mb-1">3.2</div>
                        <div class="text-gray-400 text-sm">Avg Visits/Week</div>
                        <div class="w-full bg-gray-800 rounded-full h-2 mt-3">
                            <div class="bg-gym-accent h-2 rounded-full" style="width: 64%"></div>
                        </div>
                    </div>

                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-blue/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-gym-blue text-xl"></i>
                            </div>
                            <span class="text-gym-blue text-sm">+22%</span>
                        </div>
                        <div class="text-3xl font-bold text-white mb-1">$24.5K</div>
                        <div class="text-gray-400 text-sm">Monthly Revenue</div>
                        <div class="w-full bg-gray-800 rounded-full h-2 mt-3">
                            <div class="bg-gym-blue h-2 rounded-full" style="width: 78%"></div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Analytics -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- Membership Growth Chart -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6">Membership Growth</h3>
                        <div class="relative h-64">
                            <!-- Simulated Chart with CSS -->
                            <div class="absolute bottom-0 left-0 right-0 flex items-end justify-between h-full px-4">
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 40%"></div>
                                    <span class="text-xs text-gray-400">Jan</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 55%"></div>
                                    <span class="text-xs text-gray-400">Feb</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 70%"></div>
                                    <span class="text-xs text-gray-400">Mar</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 45%"></div>
                                    <span class="text-xs text-gray-400">Apr</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 80%"></div>
                                    <span class="text-xs text-gray-400">May</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 90%"></div>
                                    <span class="text-xs text-gray-400">Jun</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mt-4 text-sm">
                            <span class="text-gray-400">Growth Rate</span>
                            <span class="text-gym-primary font-medium">+12% this month</span>
                        </div>
                    </div>

                    <!-- Peak Hours -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6">Peak Hours</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">6:00 - 8:00 AM</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-primary h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                    <span class="text-white text-sm">85%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">12:00 - 2:00 PM</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-secondary h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                    <span class="text-white text-sm">65%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">6:00 - 8:00 PM</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-accent h-2 rounded-full" style="width: 95%"></div>
                                    </div>
                                    <span class="text-white text-sm">95%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">8:00 - 10:00 PM</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-800 rounded-full h-2">
                                        <div class="bg-purple-400 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                    <span class="text-white text-sm">45%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Member Demographics -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Age Distribution -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-lg font-semibold text-white mb-4">Age Distribution</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">18-25</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-primary h-2 rounded-full" style="width: 25%"></div>
                                    </div>
                                    <span class="text-white text-sm">25%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">26-35</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-secondary h-2 rounded-full" style="width: 40%"></div>
                                    </div>
                                    <span class="text-white text-sm">40%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">36-45</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-accent h-2 rounded-full" style="width: 20%"></div>
                                    </div>
                                    <span class="text-white text-sm">20%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">46+</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-800 rounded-full h-2">
                                        <div class="bg-purple-400 h-2 rounded-full" style="width: 15%"></div>
                                    </div>
                                    <span class="text-white text-sm">15%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Membership Plans -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-lg font-semibold text-white mb-4">Plan Distribution</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gym-primary/10 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-gym-primary rounded-full"></div>
                                    <span class="text-white">Premium</span>
                                </div>
                                <span class="text-gym-primary font-medium">45%</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gym-secondary/10 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-gym-secondary rounded-full"></div>
                                    <span class="text-white">Basic</span>
                                </div>
                                <span class="text-gym-secondary font-medium">35%</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gym-blue/10 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-gym-blue rounded-full"></div>
                                    <span class="text-white">Elite</span>
                                </div>
                                <span class="text-gym-blue font-medium">20%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Top Achievements -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-lg font-semibold text-white mb-4">Top Achievements</h3>
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3 p-3 bg-yellow-500/10 rounded-lg">
                                <i class="fas fa-trophy text-yellow-400"></i>
                                <div>
                                    <div class="text-white font-medium">100 Visits</div>
                                    <div class="text-gray-400 text-sm">89 members</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 bg-gym-primary/10 rounded-lg">
                                <i class="fas fa-medal text-gym-primary"></i>
                                <div>
                                    <div class="text-white font-medium">Consistency</div>
                                    <div class="text-gray-400 text-sm">156 members</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 bg-purple-500/10 rounded-lg">
                                <i class="fas fa-star text-purple-400"></i>
                                <div>
                                    <div class="text-white font-medium">Class Regular</div>
                                    <div class="text-gray-400 text-sm">203 members</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        </div>
    </div>

    <!-- JavaScript for Navigation and Interactivity -->
    <script>
        // Theme Management
        class ThemeManager {
            constructor() {
                this.currentTheme = localStorage.getItem('gym-theme') || 'dark';
                this.init();
            }

            init() {
                // Set initial theme
                this.setTheme(this.currentTheme);

                // Add event listener to toggle button
                const toggleButton = document.getElementById('theme-toggle');
                if (toggleButton) {
                    toggleButton.addEventListener('click', () => this.toggleTheme());
                }
            }

            setTheme(theme) {
                this.currentTheme = theme;
                document.body.setAttribute('data-theme', theme);
                localStorage.setItem('gym-theme', theme);

                // Update dynamic styles that need theme-aware colors
                this.updateDynamicStyles();
            }

            toggleTheme() {
                const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
            }

            updateDynamicStyles() {
                // Update any elements that need special handling for theme changes
                const searchInput = document.querySelector('input[placeholder="Search members..."]');
                if (searchInput) {
                    searchInput.style.setProperty('--placeholder-color',
                        this.currentTheme === 'dark' ? '#9ca3af' : '#64748b');
                }

                // Update notification dropdown and other dynamic elements
                this.updateNotificationStyles();
                this.updateCardStyles();
                this.updateNavigationStyles();
                this.updateFormElements();
                this.updateAllTextElements();
            }

            updateNotificationStyles() {
                const dropdown = document.getElementById('notification-dropdown');
                if (dropdown) {
                    dropdown.style.backgroundColor = 'var(--card-bg)';
                    dropdown.style.borderColor = 'var(--border-primary)';
                }
            }

            updateCardStyles() {
                // Update all cards and sections to use CSS custom properties
                const cards = document.querySelectorAll('.bg-gym-dark, .bg-gray-800');
                cards.forEach(card => {
                    card.style.backgroundColor = 'var(--card-bg)';
                    card.style.borderColor = 'var(--border-primary)';
                });
            }

            updateNavigationStyles() {
                // Update navigation links
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    if (!link.classList.contains('active')) {
                        link.style.color = 'var(--text-secondary)';
                    } else {
                        link.style.color = 'var(--text-primary)';
                        link.style.backgroundColor = 'var(--hover-bg)';
                    }
                });
            }

            updateFormElements() {
                // Update form inputs and selects
                const inputs = document.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.style.backgroundColor = 'var(--input-bg)';
                    input.style.borderColor = 'var(--border-secondary)';
                    input.style.color = 'var(--text-primary)';
                });
            }

            updateAllTextElements() {
                // Update text elements that weren't caught by other methods
                const textElements = document.querySelectorAll('.text-white, .text-gray-100, .text-gray-200');
                textElements.forEach(element => {
                    element.style.color = 'var(--text-primary)';
                });

                const mutedTextElements = document.querySelectorAll('.text-gray-400, .text-gray-500');
                mutedTextElements.forEach(element => {
                    element.style.color = 'var(--text-muted)';
                });

                const secondaryTextElements = document.querySelectorAll('.text-gray-300');
                secondaryTextElements.forEach(element => {
                    element.style.color = 'var(--text-secondary)';
                });
            }
        }

        // Initialize theme manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.themeManager = new ThemeManager();
        });

        // Notification System Data
        let notifications = [
            {
                id: 1,
                type: 'member',
                title: 'New Member Registration',
                message: 'Sarah Johnson has registered for a Basic membership',
                timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
                read: false,
                icon: 'fa-user-plus',
                color: 'gym-secondary'
            },
            {
                id: 2,
                type: 'payment',
                title: 'Payment Overdue',
                message: 'Lisa Wilson\'s payment is 3 days overdue ($59.99)',
                timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
                read: false,
                icon: 'fa-exclamation-triangle',
                color: 'red-500'
            },
            {
                id: 3,
                type: 'class',
                title: 'Class Booking Confirmed',
                message: 'HIIT Training class is fully booked (20/20)',
                timestamp: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
                read: false,
                icon: 'fa-calendar-check',
                color: 'gym-primary'
            },
            {
                id: 4,
                type: 'payment',
                title: 'Payment Successful',
                message: 'Mike Davis paid $99.99 for Elite membership',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
                read: true,
                icon: 'fa-credit-card',
                color: 'gym-accent'
            },
            {
                id: 5,
                type: 'maintenance',
                title: 'Equipment Maintenance',
                message: 'Treadmill #3 requires scheduled maintenance',
                timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
                read: true,
                icon: 'fa-wrench',
                color: 'gym-secondary'
            },
            {
                id: 6,
                type: 'system',
                title: 'System Update',
                message: 'Gym management system updated to v2.1.0',
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
                read: true,
                icon: 'fa-info-circle',
                color: 'gym-blue'
            }
        ];

        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const openSidebar = document.getElementById('open-sidebar');
            const closeSidebar = document.getElementById('close-sidebar');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-menu-overlay');

            openSidebar.addEventListener('click', function() {
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
                // Ensure sidebar scrolling works on mobile
                document.body.style.overflow = 'hidden';
            });

            closeSidebar.addEventListener('click', function() {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                // Restore body scrolling
                document.body.style.overflow = '';
            });

            overlay.addEventListener('click', function() {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                // Restore body scrolling
                document.body.style.overflow = '';
            });

            // Navigation links
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.section');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Hide all sections
                    sections.forEach(section => section.classList.add('hidden'));

                    // Show target section
                    const target = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(target + '-section');
                    if (targetSection) {
                        targetSection.classList.remove('hidden');
                    }

                    // Update header title
                    const headerTitle = document.querySelector('header h1');
                    const linkText = this.textContent.trim();
                    headerTitle.textContent = linkText;

                    // Close mobile menu
                    if (window.innerWidth < 1024) {
                        sidebar.classList.add('-translate-x-full');
                        overlay.classList.add('hidden');
                        // Restore body scrolling
                        document.body.style.overflow = '';
                    }
                });
            });

            // Notification System
            const notificationBtn = document.getElementById('notification-btn');
            const notificationDropdown = document.getElementById('notification-dropdown');
            const notificationBadge = document.getElementById('notification-badge');
            const notificationsList = document.getElementById('notifications-list');
            const markAllReadBtn = document.getElementById('mark-all-read');
            const viewAllBtn = document.getElementById('view-all-notifications');

            // Format timestamp to relative time
            function formatTimestamp(timestamp) {
                const now = new Date();
                const diff = now - timestamp;
                const minutes = Math.floor(diff / (1000 * 60));
                const hours = Math.floor(diff / (1000 * 60 * 60));
                const days = Math.floor(diff / (1000 * 60 * 60 * 24));

                if (minutes < 1) return 'Just now';
                if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
                if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
                return `${days} day${days > 1 ? 's' : ''} ago`;
            }

            // Update notification badge count
            function updateNotificationBadge() {
                const unreadCount = notifications.filter(n => !n.read).length;
                if (unreadCount > 0) {
                    notificationBadge.textContent = unreadCount;
                    notificationBadge.classList.remove('hidden');
                } else {
                    notificationBadge.classList.add('hidden');
                }
            }

            // Render notifications
            function renderNotifications() {
                const visibleNotifications = notifications.slice(0, 7); // Show max 7 notifications

                notificationsList.innerHTML = visibleNotifications.map(notification => `
                    <div class="notification-item p-4 border-b border-gray-800 hover:bg-gray-800/30 transition-colors cursor-pointer ${notification.read ? 'opacity-60' : ''}"
                         data-id="${notification.id}">
                        <div class="flex items-start space-x-3">
                            <div class="w-10 h-10 bg-${notification.color}/20 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas ${notification.icon} text-${notification.color} text-sm"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <h4 class="text-white font-medium text-sm">${notification.title}</h4>
                                        <p class="text-gray-400 text-sm mt-1 line-clamp-2">${notification.message}</p>
                                        <p class="text-gray-500 text-xs mt-2">${formatTimestamp(notification.timestamp)}</p>
                                    </div>
                                    ${!notification.read ? '<div class="w-2 h-2 bg-gym-primary rounded-full ml-2 mt-1 flex-shrink-0"></div>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');

                updateNotificationBadge();
            }

            // Toggle notification dropdown
            notificationBtn.addEventListener('click', function(e) {
                e.stopPropagation();

                // Position dropdown relative to notification button
                const buttonRect = notificationBtn.getBoundingClientRect();
                const dropdownWidth = 320; // 80 * 4 (w-80 in Tailwind)

                // Calculate position
                let rightPosition = window.innerWidth - buttonRect.right;
                let topPosition = buttonRect.bottom + 8; // 8px gap

                // Ensure dropdown doesn't go off-screen
                if (rightPosition + dropdownWidth > window.innerWidth) {
                    rightPosition = 16; // 1rem minimum margin
                }

                if (topPosition + 400 > window.innerHeight) {
                    topPosition = buttonRect.top - 400 - 8; // Show above if not enough space below
                }

                // Apply positioning
                notificationDropdown.style.right = rightPosition + 'px';
                notificationDropdown.style.top = topPosition + 'px';

                notificationDropdown.classList.toggle('hidden');
                if (!notificationDropdown.classList.contains('hidden')) {
                    renderNotifications();
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!notificationDropdown.contains(e.target) && !notificationBtn.contains(e.target)) {
                    notificationDropdown.classList.add('hidden');
                }
            });

            // Mark notification as read when clicked
            notificationsList.addEventListener('click', function(e) {
                const notificationItem = e.target.closest('.notification-item');
                if (notificationItem) {
                    const notificationId = parseInt(notificationItem.dataset.id);
                    const notification = notifications.find(n => n.id === notificationId);
                    if (notification && !notification.read) {
                        notification.read = true;
                        renderNotifications();
                    }
                }
            });

            // Mark all notifications as read
            markAllReadBtn.addEventListener('click', function() {
                notifications.forEach(notification => {
                    notification.read = true;
                });
                renderNotifications();
            });

            // View all notifications (simulate navigation)
            viewAllBtn.addEventListener('click', function() {
                notificationDropdown.classList.add('hidden');
                // Here you would typically navigate to a dedicated notifications page
                alert('Navigate to full notifications page');
            });

            // Simulate new notifications
            function addNewNotification(type, title, message, icon, color) {
                const newNotification = {
                    id: Date.now(),
                    type: type,
                    title: title,
                    message: message,
                    timestamp: new Date(),
                    read: false,
                    icon: icon,
                    color: color
                };
                notifications.unshift(newNotification);
                updateNotificationBadge();

                // Show brief animation on bell icon
                notificationBtn.classList.add('animate-bounce');
                setTimeout(() => {
                    notificationBtn.classList.remove('animate-bounce');
                }, 1000);
            }

            // Simulate receiving new notifications periodically
            setInterval(() => {
                const randomNotifications = [
                    {
                        type: 'member',
                        title: 'New Check-in',
                        message: 'Alex Thompson just checked in',
                        icon: 'fa-sign-in-alt',
                        color: 'gym-primary'
                    },
                    {
                        type: 'class',
                        title: 'Class Cancelled',
                        message: 'Evening Yoga class has been cancelled',
                        icon: 'fa-calendar-times',
                        color: 'red-500'
                    },
                    {
                        type: 'payment',
                        title: 'Payment Reminder',
                        message: 'Monthly payment due in 3 days',
                        icon: 'fa-clock',
                        color: 'gym-secondary'
                    },
                    {
                        type: 'system',
                        title: 'System Maintenance',
                        message: 'Scheduled maintenance tonight at 2 AM',
                        icon: 'fa-tools',
                        color: 'gym-blue'
                    }
                ];

                if (Math.random() < 0.3) { // 30% chance every interval
                    const randomNotif = randomNotifications[Math.floor(Math.random() * randomNotifications.length)];
                    addNewNotification(randomNotif.type, randomNotif.title, randomNotif.message, randomNotif.icon, randomNotif.color);
                }
            }, 30000); // Every 30 seconds

            // Initialize notification system
            updateNotificationBadge();

            // Sidebar Scrolling Enhancements
            const sidebarScroll = document.querySelector('.sidebar-scroll');

            // Add smooth scrolling behavior
            if (sidebarScroll) {
                // Ensure active navigation item is visible when sidebar opens
                function scrollToActiveItem() {
                    const activeLink = sidebar.querySelector('.nav-link.active');
                    if (activeLink && sidebarScroll) {
                        setTimeout(() => {
                            const linkRect = activeLink.getBoundingClientRect();
                            const containerRect = sidebarScroll.getBoundingClientRect();

                            if (linkRect.top < containerRect.top || linkRect.bottom > containerRect.bottom) {
                                activeLink.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'center'
                                });
                            }
                        }, 300); // Wait for sidebar animation to complete
                    }
                }

                // Scroll to active item when sidebar opens on mobile
                openSidebar.addEventListener('click', scrollToActiveItem);

                // Enhanced scroll indicators with auto-hide scrollbar
                let scrollTimeout;
                let isScrolling = false;

                sidebarScroll.addEventListener('scroll', function() {
                    // Add scrolling class for visual feedback and scrollbar visibility
                    if (!isScrolling) {
                        this.classList.add('scrolling');
                        isScrolling = true;
                    }

                    clearTimeout(scrollTimeout);
                    scrollTimeout = setTimeout(() => {
                        this.classList.remove('scrolling');
                        isScrolling = false;
                    }, 1500); // Hide scrollbar after 1.5 seconds of no scrolling
                });

                // Show scrollbar on hover for better UX
                sidebarScroll.addEventListener('mouseenter', function() {
                    if (!isScrolling) {
                        this.classList.add('scrolling');
                    }
                });

                sidebarScroll.addEventListener('mouseleave', function() {
                    if (!isScrolling) {
                        clearTimeout(scrollTimeout);
                        scrollTimeout = setTimeout(() => {
                            this.classList.remove('scrolling');
                        }, 500); // Quick hide when mouse leaves
                    }
                });
            }

            // Handle window resize to ensure proper sidebar behavior
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    // Desktop view - ensure body scroll is restored
                    document.body.style.overflow = '';
                    overlay.classList.add('hidden');
                }
            });

            // Add notification triggers and navigation for quick actions
            const quickActionButtons = document.querySelectorAll('.group');
            quickActionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const buttonText = this.textContent.trim();

                    // Navigation functionality
                    if (buttonText.includes('Add New Member')) {
                        document.querySelector('a[href="#registration"]').click();
                        setTimeout(() => {
                            addNewNotification('member', 'Member Registration Started', 'New member registration form opened', 'fa-user-plus', 'gym-secondary');
                        }, 1000);
                    } else if (buttonText.includes('Quick Check-in')) {
                        document.querySelector('a[href="#checkin"]').click();
                        setTimeout(() => {
                            addNewNotification('checkin', 'Check-in System Accessed', 'Quick check-in interface opened', 'fa-sign-in-alt', 'gym-primary');
                        }, 1000);
                    } else if (buttonText.includes('Schedule Class')) {
                        document.querySelector('a[href="#schedule"]').click();
                        setTimeout(() => {
                            addNewNotification('class', 'Class Scheduler Opened', 'Class scheduling interface accessed', 'fa-calendar-plus', 'gym-accent');
                        }, 1000);
                    } else if (buttonText.includes('View Reports')) {
                        document.querySelector('a[href="#statistics"]').click();
                        setTimeout(() => {
                            addNewNotification('system', 'Reports Accessed', 'Statistics and analytics dashboard opened', 'fa-chart-line', 'purple-500');
                        }, 1000);
                    }
                });
            });

            // Quick actions functionality (merged with notification triggers above)

            // Form validation styling
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input[required], select[required]');
                inputs.forEach(input => {
                    input.addEventListener('blur', function() {
                        if (this.value.trim() === '') {
                            this.classList.add('border-red-500');
                            this.classList.remove('border-gray-700');
                        } else {
                            this.classList.remove('border-red-500');
                            this.classList.add('border-gray-700');
                        }
                    });
                });
            });

            // Member search functionality
            const searchInputs = document.querySelectorAll('input[placeholder*="Search"]');
            searchInputs.forEach(input => {
                input.addEventListener('input', function() {
                    // Simulate search functionality
                    console.log('Searching for:', this.value);
                });
            });

            // Check-in functionality simulation
            const checkinInput = document.querySelector('input[placeholder*="Member ID"]');
            if (checkinInput) {
                checkinInput.addEventListener('input', function() {
                    const memberInfo = document.getElementById('member-info');
                    if (this.value.length >= 5) {
                        memberInfo.classList.remove('hidden');
                    } else {
                        memberInfo.classList.add('hidden');
                    }
                });
            }

            // Membership plan selection
            const planRadios = document.querySelectorAll('input[name="membership"]');
            planRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    // Update UI based on selected plan
                    console.log('Selected plan:', this.value);
                });
            });

            // Table row hover effects
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.classList.add('bg-gray-800/25');
                });
                row.addEventListener('mouseleave', function() {
                    this.classList.remove('bg-gray-800/25');
                });
            });

            // Note: Notification badge animation is now handled by the notification system above
        });

        // CSS for active navigation state
        const style = document.createElement('style');
        style.textContent = `
            .nav-link.active {
                background-color: rgba(16, 185, 129, 0.2);
                color: #33AADA;
                border-left: 3px solid #33AADA;
                padding-left: 9px;
            }

            .nav-link:not(.active) {
                color: #9CA3AF;
            }

            .nav-link:not(.active):hover {
                background-color: rgba(75, 85, 99, 0.5);
                color: #F3F4F6;
            }

            .section {
                animation: fadeIn 0.3s ease-in-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            /* Layout fixes */
            .flex.h-screen {
                min-height: 100vh;
            }

            /* Ensure sidebar is properly positioned */
            @media (min-width: 1024px) {
                #sidebar {
                    position: relative !important;
                    transform: translateX(0) !important;
                }
            }

            /* Notification System Styles */
            #notification-dropdown {
                animation: slideDown 0.2s ease-out;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
                z-index: 9999 !important;
                position: fixed !important;
                top: 4rem !important;
                right: 1rem !important;
                transform: none !important;
                will-change: transform, opacity;
            }

            #notification-dropdown.hidden {
                animation: slideUp 0.2s ease-in;
            }

            /* Ensure header has proper z-index */
            header {
                z-index: 1000 !important;
                position: relative !important;
            }

            /* Notification button container positioning */
            .relative {
                position: relative;
            }

            /* Ensure proper stacking context */
            #notification-btn {
                z-index: 1001;
                position: relative;
            }

            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes slideUp {
                from {
                    opacity: 1;
                    transform: translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateY(-10px);
                }
            }

            .notification-item:hover {
                background-color: rgba(75, 85, 99, 0.3);
            }

            .line-clamp-2 {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            /* Notification badge pulse animation */
            @keyframes notificationPulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.1);
                }
            }

            .notification-pulse {
                animation: notificationPulse 2s infinite;
            }

            /* Mobile responsive adjustments for notifications */
            @media (max-width: 640px) {
                #notification-dropdown {
                    width: calc(100vw - 2rem) !important;
                    right: 1rem !important;
                    left: 1rem !important;
                    top: 4rem !important;
                    transform: none !important;
                    max-width: none !important;
                }
            }

            @media (max-width: 768px) {
                #notification-dropdown {
                    width: calc(100vw - 2rem) !important;
                    right: 1rem !important;
                    max-width: 400px !important;
                }
            }

            /* Responsive improvements */
            @media (max-width: 768px) {
                .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
                    grid-template-columns: repeat(2, 1fr);
                }

                .grid-cols-1.md\\:grid-cols-3 {
                    grid-template-columns: repeat(2, 1fr);
                }

                .text-5xl {
                    font-size: 2.5rem;
                }

                .p-8 {
                    padding: 1.5rem;
                }
            }

            @media (max-width: 640px) {
                .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
                    grid-template-columns: 1fr;
                }

                .grid-cols-1.md\\:grid-cols-3 {
                    grid-template-columns: 1fr;
                }

                .text-3xl {
                    font-size: 1.5rem;
                }

                .text-2xl {
                    font-size: 1.25rem;
                }
            }

            /* Custom scrollbar for main content */
            ::-webkit-scrollbar {
                width: 2px;
            }

            ::-webkit-scrollbar-track {
                background: #1F2937;
            }

            ::-webkit-scrollbar-thumb {
                background: #33AADA;
                border-radius: 3px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: #059669;
            }

            /* Sidebar Scrolling Styles */
            .sidebar-scroll {
                scrollbar-width: thin;
                scrollbar-color: #33AADA transparent;
            }

            .sidebar-scroll::-webkit-scrollbar {
                width: 6px;
            }

            .sidebar-scroll::-webkit-scrollbar-track {
                background: transparent;
            }

            /* Hidden scrollbar by default */
            .sidebar-scroll::-webkit-scrollbar-thumb {
                background: transparent;
                border-radius: 3px;
                transition: background-color 0.3s ease, opacity 0.3s ease;
            }

            .sidebar-scroll::-webkit-scrollbar-thumb:hover {
                background: rgba(107, 114, 128, 0.8);
            }

            /* Show scrollbar when actively scrolling - Dark theme */
            .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
                background: rgba(75, 85, 99, 0.8);
            }

            .sidebar-scroll.scrolling::-webkit-scrollbar-thumb:hover {
                background: rgba(107, 114, 128, 1);
            }

            /* Ensure smooth scrolling */
            .sidebar-scroll {
                scroll-behavior: smooth;
            }

            /* Mobile scrollbar styling */
            @media (max-width: 1023px) {
                .sidebar-scroll::-webkit-scrollbar {
                    width: 4px;
                }

                .sidebar-scroll::-webkit-scrollbar-thumb {
                    background: transparent;
                }

                .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
                    background: rgba(75, 85, 99, 0.6);
                }

                [data-theme="light"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
                    background: rgba(203, 213, 225, 0.6);
                }

                .sidebar-scroll {
                    scrollbar-width: thin;
                    scrollbar-color: transparent transparent;
                }
            }

            /* Sidebar layout improvements */
            #sidebar {
                display: flex;
                flex-direction: column;
                height: 100vh;
                max-height: 100vh;
            }

            /* Ensure navigation items have proper spacing for scrolling */
            #sidebar nav {
                min-height: 0; /* Allow flex item to shrink */
            }

            /* Additional scrolling visual enhancements */
            .sidebar-scroll.scrolling {
                /* Add subtle glow effect when scrolling */
                box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
            }

            [data-theme="light"] .sidebar-scroll.scrolling {
                box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
            }

            /* Firefox scrollbar styling */
            .sidebar-scroll {
                scrollbar-width: thin;
                scrollbar-color: transparent transparent;
            }

            .sidebar-scroll.scrolling {
                scrollbar-color: rgba(75, 85, 99, 0.8) transparent;
            }

            [data-theme="light"] .sidebar-scroll.scrolling {
                scrollbar-color: rgba(203, 213, 225, 0.8) transparent;
            }

            /* Ensure scrollbar area doesn't interfere with content */
            .sidebar-scroll {
                padding-right: 2px;
            }

            /* Fade effect for overflowing content */
            .sidebar-scroll::before,
            .sidebar-scroll::after {
                content: '';
                position: absolute;
                left: 0;
                right: 0;
                height: 20px;
                pointer-events: none;
                z-index: 1;
                transition: opacity 0.3s ease;
            }

            .sidebar-scroll::before {
                top: 0;
                background: linear-gradient(to bottom, #111827, transparent);
                opacity: 0;
            }

            .sidebar-scroll::after {
                bottom: 0;
                background: linear-gradient(to top, #111827, transparent);
                opacity: 0;
            }

            /* Show fade effects when scrolling */
            .sidebar-scroll.scrolling::before,
            .sidebar-scroll.scrolling::after {
                opacity: 1;
            }

            /* Ensure proper touch scrolling on mobile */
            .sidebar-scroll {
                -webkit-overflow-scrolling: touch;
                overscroll-behavior: contain;
            }

            /* Additional mobile optimizations */
            @media (max-width: 1023px) {
                .sidebar-scroll {
                    /* Improve touch scrolling performance */
                    transform: translateZ(0);
                    will-change: scroll-position;
                }

                /* Adjust padding for mobile scrolling */
                #sidebar nav {
                    padding-bottom: 2rem;
                }
            }

            /* Gym Blue Color Enhancements */
            .text-gym-blue {
                color: #337ADE;
            }

            .bg-gym-blue {
                background-color: #337ADE;
            }

            .border-gym-blue {
                border-color: #337ADE;
            }

            .hover\\:bg-gym-blue\\/80:hover {
                background-color: rgba(51, 122, 222, 0.8);
            }

            .hover\\:bg-gym-blue\\/30:hover {
                background-color: rgba(51, 122, 222, 0.3);
            }

            .hover\\:border-gym-blue\\/50:hover {
                border-color: rgba(51, 122, 222, 0.5);
            }

            /* Enhanced hover effects with gym-blue */
            .hover\\:text-gym-blue:hover {
                color: #337ADE;
                transition: color 0.2s ease;
            }

            /* Focus states for accessibility */
            .focus\\:ring-gym-blue:focus {
                --tw-ring-color: rgba(51, 122, 222, 0.5);
            }

            /* Gradient effects with gym-blue */
            .bg-gradient-to-r.from-gym-blue {
                background-image: linear-gradient(to right, #337ADE, var(--tw-gradient-to));
            }

            /* Box shadow effects */
            .shadow-gym-blue {
                box-shadow: 0 4px 14px 0 rgba(51, 122, 222, 0.15);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>